; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:seeed_xiao_esp32s3]
platform = espressif32 @ 6.10.0
board = seeed_xiao_esp32s3
framework = arduino
monitor_speed = 115200

lib_deps = 
    bblanchon/Ard<PERSON>o<PERSON><PERSON>@^7.1.0
    links2004/WebSockets@^2.4.1
    ESP32Async/ESPAsyncWebServer@^3.7.6
    https://github.com/esp-arduino-libs/ESP32_Button.git#v0.0.1
    https://github.com/pschatzmann/arduino-audio-tools.git#v1.0.1
    https://github.com/pschatzmann/arduino-libopus.git#a1.1.0
    https://github.com/pschatzmann/arduino-libhelix.git

; board_build.arduino.memory_type = qio_opi
; board_build.flash_mode = qio
; board_build.prsam_type = opi
board_upload.flash_size = 8MB
board_upload.maximum_size = 8388608
board_build.filesystem = spiffs
board_build.partitions = partition.csv
upload_protocol = esptool
monitor_filters =
  esp32_exception_decoder
  time
build_unflags = -std=gnu++11

build_flags = 
    -std=gnu++17
    -DCORE_DEBUG_LEVEL=5
    -D DEBUG_ESP_PORT=Serial

    ; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html
